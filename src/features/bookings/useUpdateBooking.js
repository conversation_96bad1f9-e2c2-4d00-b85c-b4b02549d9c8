import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateBooking } from '@/services/apiBookings'
import toast from 'react-hot-toast'

export function useUpdateBooking() {
  const queryClient = useQueryClient()

  const { mutate: updateBookingMutation, isLoading: isUpdating } = useMutation({
    mutationFn: ({ id, obj }) => updateBooking(id, obj),
    onSuccess: (data) => {
      toast.dismiss('update-booking')
      toast.success(`Booking #${data.id} successfully updated`)
      queryClient.invalidateQueries({ queryKey: ['booking'] })
      queryClient.invalidateQueries({ queryKey: ['bookings'] })
    },
    onError: err => {
      toast.dismiss('update-booking')
      toast.error(err.message)
    },
    retry: (failureCount, error) => {
      return failureCount < 2 && !error.timeout
    },
    retryDelay: 150,
    onMutate: () => {
      if (!navigator.onLine) {
        toast.error('You are offline. Please check your internet connection')
        return
      }
      toast.loading('Updating booking...', { id: 'update-booking' })
    }
  })

  return { updateBookingMutation, isUpdating }
}
