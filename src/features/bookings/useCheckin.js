import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateBooking } from '@/services/apiBookings'
import toast from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

export function useCheckin() {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const { mutate: checkin, isLoading: isCheckingIn } = useMutation({
    mutationFn: ({ bookingId, breakfast }) =>
      updateBooking(bookingId, {
        status: 'checked-in',
        isPaid: true,
        ...breakfast
      }),
    onSuccess: data => {
      toast.dismiss('checkin-booking')
      toast.success(`Booking #${data.id} successfully checked in`)
      queryClient.invalidateQueries({ active: true })
      navigate('/')
    },
    onError: err => {
      toast.dismiss('checkin-booking')
      toast.error(err.message)
    },
    retry: (failureCount, error) => {
      return failureCount < 2 && !error.timeout
    },
    retryDelay: 150,
    onMutate: () => {
      if (!navigator.onLine) {
        toast.error('You are offline. Please check your internet connection')
        return
      }
      toast.loading('Checking in...', { id: 'checkin-booking' })
    }
  })

  return { checkin, isCheckingIn }
}
