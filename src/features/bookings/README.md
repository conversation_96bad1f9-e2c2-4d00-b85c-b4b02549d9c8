# Bookings Hooks

Este diretório contém todos os hooks relacionados ao gerenciamento de reservas (bookings).

## Hooks Disponíveis

### 1. `useBookings()`
Hook principal para buscar lista de reservas com suporte a filtros, ordenação e paginação.

**Retorna:**
- `isLoading`: boolean - Estado de carregamento
- `error`: object - Erro se houver
- `bookings`: array - Lista de reservas
- `count`: number - Total de reservas (para paginação)

**Funcionalidades:**
- Filtragem por status via URL params (`?status=checked-in`)
- Ordenação via URL params (`?sortBy=startDate-desc`)
- Paginação via URL params (`?page=2`)
- Pre-fetching automático das páginas adjacentes

**Exemplo de uso:**
```jsx
import { useBookings } from './useBookings'

function BookingTable() {
  const { bookings, isLoading, count } = useBookings()
  
  if (isLoading) return <Spinner />
  
  return (
    <Table>
      {bookings.map(booking => (
        <BookingRow key={booking.id} booking={booking} />
      ))}
    </Table>
  )
}
```

### 2. `useBooking()`
Hook para buscar uma reserva específica por ID.

**Retorna:**
- `isLoading`: boolean - Estado de carregamento
- `error`: object - Erro se houver
- `booking`: object - Dados da reserva

**Exemplo de uso:**
```jsx
import { useBooking } from './useBooking'

function BookingDetail() {
  const { booking, isLoading, error } = useBooking()
  
  if (isLoading) return <Spinner />
  if (error) return <Error message={error.message} />
  
  return <BookingDataBox booking={booking} />
}
```

### 3. `useDeleteBooking()`
Hook para deletar uma reserva.

**Retorna:**
- `isDeleting`: boolean - Estado de carregamento
- `deleteBooking`: function - Função para deletar

**Exemplo de uso:**
```jsx
import { useDeleteBooking } from './useDeleteBooking'

function BookingRow({ booking }) {
  const { deleteBooking, isDeleting } = useDeleteBooking()
  
  return (
    <button 
      onClick={() => deleteBooking(booking.id)}
      disabled={isDeleting}
    >
      Delete
    </button>
  )
}
```

### 4. `useUpdateBooking()`
Hook genérico para atualizar uma reserva.

**Retorna:**
- `isUpdating`: boolean - Estado de carregamento
- `updateBookingMutation`: function - Função para atualizar

**Exemplo de uso:**
```jsx
import { useUpdateBooking } from './useUpdateBooking'

function BookingActions({ booking }) {
  const { updateBookingMutation, isUpdating } = useUpdateBooking()
  
  const handleUpdate = () => {
    updateBookingMutation({
      id: booking.id,
      obj: { isPaid: true }
    })
  }
  
  return (
    <button onClick={handleUpdate} disabled={isUpdating}>
      Mark as Paid
    </button>
  )
}
```

### 5. `useCheckin()`
Hook específico para fazer check-in de uma reserva.

**Retorna:**
- `isCheckingIn`: boolean - Estado de carregamento
- `checkin`: function - Função para check-in

**Exemplo de uso:**
```jsx
import { useCheckin } from './useCheckin'

function CheckinBooking({ booking }) {
  const { checkin, isCheckingIn } = useCheckin()
  
  const handleCheckin = () => {
    checkin({
      bookingId: booking.id,
      breakfast: {
        hasBreakfast: true,
        extrasPrice: 50,
        totalPrice: booking.totalPrice + 50
      }
    })
  }
  
  return (
    <button onClick={handleCheckin} disabled={isCheckingIn}>
      Check In
    </button>
  )
}
```

### 6. `useCheckout()`
Hook específico para fazer check-out de uma reserva.

**Retorna:**
- `isCheckingOut`: boolean - Estado de carregamento
- `checkout`: function - Função para check-out

**Exemplo de uso:**
```jsx
import { useCheckout } from './useCheckout'

function CheckoutButton({ bookingId }) {
  const { checkout, isCheckingOut } = useCheckout()
  
  return (
    <button 
      onClick={() => checkout(bookingId)} 
      disabled={isCheckingOut}
    >
      Check Out
    </button>
  )
}
```

## Configuração da API

Os hooks utilizam as funções da API definidas em `@/services/apiBookings.js`:

- `getBookings({ filter, sortBy, page })` - Busca lista de reservas
- `getBooking(id)` - Busca reserva específica
- `updateBooking(id, obj)` - Atualiza reserva
- `deleteBooking(id)` - Deleta reserva

## Paginação

A paginação é configurada com `PAGE_SIZE = 10` por padrão. Para alterar, modifique a constante em `@/services/apiBookings.js`.

## Filtros e Ordenação

Os filtros e ordenação são controlados via URL search params:

- **Status**: `?status=all|checked-in|checked-out|unconfirmed`
- **Ordenação**: `?sortBy=field-direction` (ex: `startDate-desc`, `totalPrice-asc`)
- **Página**: `?page=1`

## Toast Notifications

Todos os hooks incluem notificações toast automáticas para:
- Estados de loading
- Sucesso nas operações
- Tratamento de erros
- Verificação de conectividade offline
