import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateBooking } from '@/services/apiBookings'
import toast from 'react-hot-toast'

export function useCheckout() {
  const queryClient = useQueryClient()

  const { mutate: checkout, isLoading: isCheckingOut } = useMutation({
    mutationFn: bookingId =>
      updateBooking(bookingId, {
        status: 'checked-out'
      }),
    onSuccess: data => {
      toast.dismiss('checkout-booking')
      toast.success(`Booking #${data.id} successfully checked out`)
      queryClient.invalidateQueries({ active: true })
    },
    onError: err => {
      toast.dismiss('checkout-booking')
      toast.error(err.message)
    },
    retry: (failureCount, error) => {
      return failureCount < 2 && !error.timeout
    },
    retryDelay: 150,
    onMutate: () => {
      if (!navigator.onLine) {
        toast.error('You are offline. Please check your internet connection')
        return
      }
      toast.loading('Checking out...', { id: 'checkout-booking' })
    }
  })

  return { checkout, isCheckingOut }
}
