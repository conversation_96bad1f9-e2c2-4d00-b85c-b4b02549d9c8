import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteBooking as deleteBooking<PERSON><PERSON> } from '@/services/apiBookings'
import toast from 'react-hot-toast'

export function useDeleteBooking() {
  const queryClient = useQueryClient()

  const { isLoading: isDeleting, mutate: deleteBooking } = useMutation({
    mutationFn: deleteBookingApi,
    onSuccess: () => {
      toast.dismiss('delete-booking')
      toast.success('Booking successfully deleted')
      queryClient.invalidateQueries({
        queryKey: ['bookings']
      })
    },
    onError: err => {
      toast.dismiss('delete-booking')
      toast.error(err?.timeout ? 'Network timeout - Please check your connection' : err.message)
    },
    retry: (failureCount, error) => {
      // Only retry twice and not if we got a timeout error
      return failureCount < 2 && !error.timeout
    },
    retryDelay: 150,
    onMutate: () => {
      // Show pending toast when mutation starts
      if (!navigator.onLine) {
        toast.error('You are offline. Please check your internet connection')
        return
      }
      toast.loading('Deleting booking...', { id: 'delete-booking' })
    }
  })

  return { isDeleting, deleteBooking }
}
